import { useState } from 'react';
import LinkSentMessage from './components/LinkSentMessage';
import ResetPasswordForm from './components/ResetPasswordForm';

export default function ResetPasswordPage() {
  const [email, setEmail] = useState<string | undefined>();

  const renderStep = () => {
    if (email) return <LinkSentMessage email={email} />;
    return <ResetPasswordForm onLinkSent={setEmail} />;
  };

  return (
    <div className="h-screen w-full flex">
      <div className="flex flex-1 justify-center items-center">
        {renderStep()}
      </div>

      <div className="hidden md:flex h-screen flex-1 w-full items-center justify-center bg-gradient-to-b from-teal-300/70 to-primary">
        <div>
          <img
            src="/imgs/bencorpPortalCliente.png"
            alt="Logo da Onyma por Bencorp"
          />
        </div>
      </div>
    </div>
  );
}
