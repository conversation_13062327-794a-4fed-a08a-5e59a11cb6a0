import { useState } from 'react';
import { ChangeDefaultPasswordForm, PasswordChangedSuccessfully } from '..';

export default function Main() {
  const [isSuccessful, setIsSuccessful] = useState(false);

  const renderContent = () => {
    if (isSuccessful) return <PasswordChangedSuccessfully />;
    return (
      <ChangeDefaultPasswordForm onSuccess={() => setIsSuccessful(true)} />
    );
  };

  return (
    <div className="h-screen w-full flex">
      <div className="flex flex-1 justify-center items-center">
        {renderContent()}
      </div>

      <div className="hidden md:flex h-screen flex-1 w-full items-center justify-center bg-gradient-to-b from-teal-300/70 to-primary">
        <div>
          <img
            src="/imgs/bencorpPortalCliente.png"
            alt="Logo da Onyma por Bencorp"
          />
        </div>
      </div>
    </div>
  );
}
