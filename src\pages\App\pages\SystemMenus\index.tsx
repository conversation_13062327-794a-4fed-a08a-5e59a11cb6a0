import { Icons } from '@/components';
import { PageHeader } from '../../components';
import { MenusContextProvider } from './contexts/menus';
import { Menus } from './components';
import * as SC from './styles';

export default function SystemMenusPage() {
  return (
    <SC.Container>
      <PageHeader.Root>
        <PageHeader.TitleBox>
          <Icons.RAFa6.FaFolderOpen size={24} />
          <PageHeader.Title>Sistema</PageHeader.Title>
        </PageHeader.TitleBox>
        <PageHeader.BackButton />
      </PageHeader.Root>
      <MenusContextProvider>
        <Menus />
      </MenusContextProvider>
    </SC.Container>
  );
}
