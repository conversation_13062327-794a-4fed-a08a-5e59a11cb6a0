import { useLocation, useNavigate } from 'react-router-dom';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from '../ui/breadcrumb';

export function BreadcrumbMenu() {
  const location = useLocation();
  const navigate = useNavigate();

  const isHome = location.pathname.includes('/home');
  const isIndicators =
    location.pathname.includes('/indicadores') ||
    location.pathname.includes('/bi');
  const isApps = location.pathname.includes('/apps');
  const isSystem = location.pathname.includes('/sistema');
  const isFaq = location.pathname.includes('/faq');

  return (
    <div className="w-full h-16 bg-[#27B5BF1F] px-6 flex items-center shadow-sm">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink
              href={
                isHome
                  ? '/app/home'
                  : isIndicators
                    ? '/app/indicadores'
                    : isApps
                      ? '/app/apps'
                      : isSystem
                        ? '/app/sistema'
                        : isFaq
                          ? '/app/faq'
                          : '/app/home'
              }
            >
              {isHome
                ? 'Início'
                : isIndicators
                  ? 'Indicadores'
                  : isApps
                    ? 'Apps'
                    : isSystem
                      ? 'Sistema'
                      : isFaq
                        ? 'FAQ'
                        : 'Voltar'}
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink>
              {isHome || isIndicators || isApps || isSystem || isFaq
                ? ''
                : location.pathname.split('/').pop()}
            </BreadcrumbLink>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    </div>
  );
}
