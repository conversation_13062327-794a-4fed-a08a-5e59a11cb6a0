import { ApiResult } from '@/@types/ApiResult';

type Params = {
  ativo: boolean;
  id: string;
  nome: string;
  descricao: string;
  empresas: string[];
  perfis: string[];
  iframe: boolean;
  submenu: boolean;
  ordem: number;
  menuTipo: string;
  modulo: string | null;
  url: string | null;
  agendaId: number | null;
  imagem: string | null;
  parente: string | null;
  tipoCompromissoId: string | null;
  icone?: null;
  altura?: null;
  biId?: string | number | null;
  biPaginaId?: string | number | null;
};

export type Result = ApiResult<null>;

export type RemoteUpdateMenu = (params: Params) => Promise<Result>;
